import type React from "react"
import type { ReactNode } from "react"
import { View, StyleSheet, type StyleProp, type ViewStyle } from "react-native"
import { useTheme } from "../context/ThemeContext"

interface CardProps {
  children: ReactNode
  style?: StyleProp<ViewStyle>
  variant?: "default" | "elevated" | "outlined"
}

const Card: React.FC<CardProps> = ({ children, style, variant = "default" }) => {
  const theme = useTheme()

  const getCardStyle = () => {
    switch (variant) {
      case "elevated":
        return {
          backgroundColor: theme.colors.card,
          borderRadius: theme.borderRadius.md,
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3,
        }
      case "outlined":
        return {
          backgroundColor: theme.colors.card,
          borderRadius: theme.borderRadius.md,
          borderWidth: 1,
          borderColor: theme.colors.border,
        }
      default:
        return {
          backgroundColor: theme.colors.card,
          borderRadius: theme.borderRadius.md,
          borderWidth: 1,
          borderColor: theme.colors.border,
        }
    }
  }

  const styles = StyleSheet.create({
    card: {
      ...getCardStyle(),
      overflow: "hidden",
    },
  })

  return <View style={[styles.card, style]}>{children}</View>
}

export default Card
