# CleanConnect React Native App Restructuring Plan

## Overview
This plan outlines the systematic restructuring of the CleanConnect React Native app to use modern React Native tools and best practices while preserving all existing UI styling and functionality.

## Current Issues Identified

### 1. **Dependency Issues**
- Unused dependencies: `@radix-ui/react-avatar`, `tailwind-merge`
- Web-specific dependencies that aren't needed for mobile-first app
- Duplicate image picker libraries (`expo-image-picker` and `react-native-image-picker`)

### 2. **Code Structure Issues**
- Duplicate responsive hooks (`useResponsive` vs `useMobile`)
- Web-specific components in mobile app (`WebNavigation`, `ResponsiveText` with web props)
- Incorrect import paths (useTheme hook imported from wrong location)
- "use client" directives (Next.js specific, not needed in React Native)
- Redundant responsive components

### 3. **File Organization Issues**
- Mixed web and mobile components in same directories
- Unused web-specific files (`components/ui/`, `styles/globals.css`)
- Inconsistent component organization

## Restructuring Steps

### Phase 1: Clean Up Dependencies
1. Remove unused dependencies
2. Consolidate duplicate libraries
3. Update package.json scripts

### Phase 2: Fix Import Issues and Remove Web-Specific Code
1. Fix incorrect useTheme import in ResponsiveText
2. Remove "use client" directives
3. Remove web-specific components and files
4. Clean up responsive hooks

### Phase 3: Reorganize File Structure
1. Consolidate components into logical groups
2. Remove redundant responsive components
3. Optimize component hierarchy

### Phase 4: Modernize Components
1. Ensure all components use modern React Native patterns
2. Optimize hook usage
3. Improve TypeScript usage

### Phase 5: Final Cleanup
1. Remove unused files
2. Update imports throughout the app
3. Verify all functionality is preserved

## Detailed Implementation Plan

### 1. Dependencies to Remove
```json
{
  "dependencies": {
    "@radix-ui/react-avatar": "REMOVE - Web component, not needed",
    "tailwind-merge": "REMOVE - Not used in React Native context",
    "react-native-image-picker": "REMOVE - Duplicate of expo-image-picker"
  }
}
```

### 2. Files to Remove
- `components/ui/` (entire directory - web-specific)
- `styles/globals.css` (web-specific)
- `src/components/navigation/WebNavigation.tsx`
- `src/hooks/useMobile.tsx` (duplicate functionality)
- `components/ui/use-mobile.tsx` (web-specific)

### 3. Components to Fix
- `ResponsiveText.tsx` - Fix import and remove web-specific code
- `ResponsiveImage.tsx` - Remove web-specific props
- `ResponsiveContainer.tsx` - Simplify for mobile-only
- All components with "use client" directive

### 4. File Structure Reorganization
```
src/
├── components/
│   ├── common/           # Reusable UI components
│   │   ├── Button.tsx
│   │   ├── Card.tsx
│   │   ├── Badge.tsx
│   │   └── Toast.tsx
│   ├── forms/            # Form-related components
│   │   ├── AuthModal.tsx
│   │   └── OtpVerificationModal.tsx
│   ├── layout/           # Layout components
│   │   ├── Header.tsx
│   │   └── AppLayout.tsx
│   ├── location/         # Location-related components
│   │   ├── LocationPermissionModal.tsx
│   │   ├── LocationSelector.tsx
│   │   └── LocationSelectorBottomSheet.tsx
│   ├── provider/         # Provider-specific components
│   │   ├── ProviderAvailabilityCalendar.tsx
│   │   └── ProviderSelection.tsx
│   ├── service/          # Service-related components
│   │   ├── ServiceCard.tsx
│   │   └── ServiceListItem.tsx
│   └── navigation/       # Navigation components (mobile only)
│       └── MobileNavigation.tsx
├── hooks/
│   └── useResponsive.tsx # Single responsive hook
├── context/              # React contexts
├── navigation/           # Navigation configuration
├── screens/              # Screen components
├── services/             # Data services
├── types/                # TypeScript types
├── utils/                # Utility functions
├── data/                 # Static data
└── config/               # Configuration files
```

## Implementation Priority

### High Priority (Core Functionality)
1. Fix import errors
2. Remove web-specific code
3. Clean up dependencies

### Medium Priority (Organization)
1. Reorganize file structure
2. Consolidate duplicate components
3. Optimize responsive system

### Low Priority (Polish)
1. Improve TypeScript usage
2. Add missing accessibility features
3. Optimize performance

## Success Criteria
- [ ] All existing functionality preserved
- [ ] All UI styling and colors maintained
- [ ] No import errors or warnings
- [ ] Reduced bundle size
- [ ] Cleaner, more maintainable code structure
- [ ] Modern React Native patterns throughout
- [ ] Cross-platform compatibility (Android, iOS, Web)
